// CRITICAL TEST: Add global marker before any imports
console.log('🚨 GLOBAL TEST: JavaScript is executing!');
window.JAVASCRIPT_EXECUTING = true;

import React, { StrictMode } from "react";
// Explicit JSX runtime import for compatibility
import { jsx, jsxs, Fragment } from "react/jsx-runtime";
import { createRoot } from "react-dom/client";
import { HashRouter as Router } from "react-router-dom";
import { Hero<PERSON>Provider } from "@heroui/react";
import { UserProvider } from "./contexts/supabase-auth.context.jsx";
import ThemeProvider from "./contexts/theme.context.jsx";
import FeatureFlagProvider from "./contexts/feature-flags.context.jsx";
import App from "./App.jsx";
import ExtensionErrorBoundary from "./components/error/ExtensionErrorBoundary.jsx";

// Import CSS
import "./styles/tailwind.css";

// Import and initialize browser extension error handler
import { initBrowserExtensionHandler } from "./utils/browserExtensionHandler.js";

// Initialize browser extension error handler early
initBrowserExtensionHandler();

// CRITICAL DEBUG: Test if main.jsx is executing at all
console.log('🚨 CRITICAL DEBUG: main.jsx file is executing!');
window.MAIN_JSX_EXECUTED = true;
document.title = 'MAIN.JSX EXECUTED - ' + document.title;

try {
  console.log('🚨 CRITICAL DEBUG: React import:', React);
  console.log('🚨 CRITICAL DEBUG: createRoot import:', createRoot);
} catch (error) {
  console.error('🚨 CRITICAL ERROR: Failed to import React or createRoot:', error);
  window.REACT_IMPORT_ERROR = error;
}

// Simple test component
function TestApp() {
  return (
    <div>
      <h1>🎉 React is Working!</h1>
      <p>This is a minimal React app to test if the flat import issue is resolved.</p>
    </div>
  );
}

// DEBUG: Test if React works at all
console.log('🔍 DEBUG: main.jsx executing - React should be working');

// Test if we can create a root at all
try {
  console.log('🔍 DEBUG: Attempting to create React root...');
  const root = createRoot(document.getElementById("root"));
  console.log('🔍 DEBUG: React root created successfully:', root);

  console.log('🔍 DEBUG: Attempting to render React app...');
  root.render(
    <StrictMode>
      <ExtensionErrorBoundary>
        <Router>
          <UserProvider>
            <ThemeProvider>
              <HeroUIProvider>
                <FeatureFlagProvider>
                  <App />
                </FeatureFlagProvider>
              </HeroUIProvider>
            </ThemeProvider>
          </UserProvider>
        </Router>
      </ExtensionErrorBoundary>
    </StrictMode>
  );
  console.log('🔍 DEBUG: React app render call completed');
} catch (error) {
  console.error('🚨 CRITICAL ERROR in main.jsx:', error);
  console.error('🚨 Error stack:', error.stack);
}
