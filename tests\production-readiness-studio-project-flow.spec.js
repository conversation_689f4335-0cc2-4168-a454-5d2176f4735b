import { test, expect } from '@playwright/test';

const PRODUCTION_URL = 'https://royalty.technology';
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

test.describe('Production Readiness: Comprehensive Studio-Project Creation Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to production site
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    
    // Login with test credentials
    console.log('🔐 Logging in with test credentials...');
    await page.fill('input[type="email"]', TEST_CREDENTIALS.email);
    await page.fill('input[type="password"]', TEST_CREDENTIALS.password);
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    
    // Verify successful login (redirects to dashboard)
    await expect(page).toHaveURL(/dashboard/);
    console.log('✅ Successfully logged in');
  });

  test('Complete Studio-Project Creation Flow with Legal Compliance', async ({ page }) => {
    console.log('🚀 Testing comprehensive studio-project creation flow...');

    // Step 1: Navigate to project creation
    console.log('📝 Step 1: Navigating to project creation...');
    await page.goto(`${PRODUCTION_URL}/project/wizard`);
    await page.waitForLoadState('networkidle');

    // Verify we're on the project wizard
    await expect(page).toHaveURL(/\/project\/wizard/);
    console.log('✅ Project wizard loaded');

    // Step 2: Test Studio Creation Flow (5-step comprehensive wizard)
    console.log('🏢 Step 2: Testing comprehensive studio creation...');
    
    // Should show studio selection/creation step first
    const studioCreationButton = page.locator('button:has-text("Create"), button:has-text("Studio")').first();
    if (await studioCreationButton.isVisible()) {
      await studioCreationButton.click();
      await page.waitForTimeout(1000);
      
      // Step 2.1: Business Type Selection
      console.log('📋 Step 2.1: Business type selection...');
      const businessTypeRadio = page.locator('input[value="established_business"], label:has-text("Established Business")').first();
      if (await businessTypeRadio.isVisible()) {
        await businessTypeRadio.click();
        await page.waitForTimeout(500);
        
        // Click Next
        const nextButton = page.locator('button:has-text("Next")').first();
        if (await nextButton.isVisible()) {
          await nextButton.click();
          await page.waitForTimeout(1000);
        }
      }
      
      // Step 2.2: Basic Information
      console.log('📝 Step 2.2: Basic studio information...');
      const studioNameInput = page.locator('input[label*="Studio"], input[placeholder*="studio"], input[placeholder*="Studio"]').first();
      if (await studioNameInput.isVisible()) {
        await studioNameInput.fill('Test Legal Studio LLC');
        await page.waitForTimeout(500);
      }
      
      const industrySelect = page.locator('select, [role="combobox"]').filter({ hasText: /industry|Industry/ }).first();
      if (await industrySelect.isVisible()) {
        await industrySelect.click();
        await page.waitForTimeout(500);
        const techOption = page.locator('option:has-text("Technology"), li:has-text("Technology")').first();
        if (await techOption.isVisible()) {
          await techOption.click();
        }
      }
      
      // Click Next
      const nextButton2 = page.locator('button:has-text("Next")').first();
      if (await nextButton2.isVisible()) {
        await nextButton2.click();
        await page.waitForTimeout(1000);
      }
      
      // Step 2.3: Legal Entity Information
      console.log('⚖️ Step 2.3: Legal entity information...');
      const legalNameInput = page.locator('input[label*="Legal"], input[placeholder*="Legal"]').first();
      if (await legalNameInput.isVisible()) {
        await legalNameInput.fill('Test Legal Studio LLC');
        await page.waitForTimeout(500);
      }
      
      const companyTypeSelect = page.locator('select, [role="combobox"]').filter({ hasText: /company|entity|LLC/ }).first();
      if (await companyTypeSelect.isVisible()) {
        await companyTypeSelect.click();
        await page.waitForTimeout(500);
        const llcOption = page.locator('option:has-text("LLC"), li:has-text("LLC")').first();
        if (await llcOption.isVisible()) {
          await llcOption.click();
        }
      }
      
      // Click Next
      const nextButton3 = page.locator('button:has-text("Next")').first();
      if (await nextButton3.isVisible()) {
        await nextButton3.click();
        await page.waitForTimeout(1000);
      }
      
      // Step 2.4: Address Information
      console.log('📍 Step 2.4: Address information...');
      const addressInput = page.locator('input[label*="Address"], input[placeholder*="address"]').first();
      if (await addressInput.isVisible()) {
        await addressInput.fill('123 Legal Street, Suite 100');
        await page.waitForTimeout(500);
      }
      
      const cityInput = page.locator('input[label*="City"], input[placeholder*="city"]').first();
      if (await cityInput.isVisible()) {
        await cityInput.fill('San Francisco');
        await page.waitForTimeout(500);
      }
      
      const stateInput = page.locator('input[label*="State"], input[placeholder*="state"]').first();
      if (await stateInput.isVisible()) {
        await stateInput.fill('CA');
        await page.waitForTimeout(500);
      }
      
      // Click Next
      const nextButton4 = page.locator('button:has-text("Next")').first();
      if (await nextButton4.isVisible()) {
        await nextButton4.click();
        await page.waitForTimeout(1000);
      }
      
      // Step 2.5: Contact & Signatory
      console.log('👤 Step 2.5: Contact and signatory information...');
      const emailInput = page.locator('input[type="email"], input[label*="Email"]').first();
      if (await emailInput.isVisible()) {
        await emailInput.fill('<EMAIL>');
        await page.waitForTimeout(500);
      }
      
      const signerNameInput = page.locator('input[label*="Signer"], input[placeholder*="signer"]').first();
      if (await signerNameInput.isVisible()) {
        await signerNameInput.fill('John Legal Smith');
        await page.waitForTimeout(500);
      }
      
      const signerTitleInput = page.locator('input[label*="Title"], input[placeholder*="title"]').first();
      if (await signerTitleInput.isVisible()) {
        await signerTitleInput.fill('Managing Member');
        await page.waitForTimeout(500);
      }
      
      // Create Studio
      const createStudioButton = page.locator('button:has-text("Create Studio")').first();
      if (await createStudioButton.isVisible()) {
        await createStudioButton.click();
        await page.waitForTimeout(3000); // Wait for studio creation
      }
      
      console.log('✅ Studio creation flow completed');
    }

    // Step 3: Test Project Creation with Auto-populated Legal Information
    console.log('📋 Step 3: Testing project creation with legal integration...');
    
    // Fill project basic information
    const projectNameInput = page.locator('input[label*="Project"], input[placeholder*="project"]').first();
    if (await projectNameInput.isVisible()) {
      await projectNameInput.fill('Test Legal Compliance Project');
      await page.waitForTimeout(500);
    }
    
    const projectDescInput = page.locator('textarea[label*="Description"], textarea[placeholder*="description"]').first();
    if (await projectDescInput.isVisible()) {
      await projectDescInput.fill('A comprehensive test project to verify legal compliance integration between studio and project creation workflows.');
      await page.waitForTimeout(500);
    }
    
    // Check if legal information is auto-populated from studio
    console.log('🔍 Verifying legal information auto-population...');
    const companyNameInput = page.locator('input[label*="Company"], input[value*="Test Legal Studio"]').first();
    if (await companyNameInput.isVisible()) {
      const companyNameValue = await companyNameInput.inputValue();
      console.log(`📊 Company name auto-populated: ${companyNameValue}`);
      expect(companyNameValue).toContain('Test Legal Studio');
    }
    
    // Verify studio integration toggle
    const studioToggle = page.locator('input[type="checkbox"], [role="switch"]').filter({ hasText: /studio|Studio/ }).first();
    if (await studioToggle.isVisible()) {
      console.log('✅ Studio integration toggle found');
    }
    
    // Step 4: Complete project creation
    console.log('🚀 Step 4: Completing project creation...');
    const nextProjectButton = page.locator('button:has-text("Next"), button:has-text("Continue")').first();
    if (await nextProjectButton.isVisible()) {
      await nextProjectButton.click();
      await page.waitForTimeout(2000);
    }
    
    // Step 5: Verify successful creation and legal compliance
    console.log('✅ Step 5: Verifying successful creation...');
    
    // Should be on project page or next step of wizard
    const currentUrl = page.url();
    console.log(`📍 Current URL: ${currentUrl}`);
    
    // Look for success indicators
    const successIndicators = [
      'success', 'created', 'completed', 'project', 'studio',
      'legal', 'compliance', 'agreement', 'ready'
    ];
    
    let foundSuccess = false;
    for (const indicator of successIndicators) {
      const element = page.locator(`text=${indicator}`).first();
      if (await element.isVisible()) {
        console.log(`✅ Found success indicator: ${indicator}`);
        foundSuccess = true;
        break;
      }
    }
    
    if (!foundSuccess) {
      console.log('⚠️ No explicit success indicators found, checking page state...');
      // Check if we're on a valid project-related page
      expect(currentUrl).toMatch(/project|track|studio/);
    }
    
    console.log('🎉 Comprehensive studio-project creation flow test completed successfully!');
  });

  test('Verify Legal Compliance Integration', async ({ page }) => {
    console.log('⚖️ Testing legal compliance integration...');

    // Navigate to project creation
    await page.goto(`${PRODUCTION_URL}/project/wizard`);
    await page.waitForLoadState('networkidle');

    // Click on "Create New Studio" to trigger the legal compliance flow
    const createStudioButton = page.locator('button:has-text("Create New Studio"), button:has-text("Create Studio"), button:has-text("New Studio")').first();
    if (await createStudioButton.isVisible()) {
      await createStudioButton.click();
      await page.waitForLoadState('networkidle');
      console.log('✅ Clicked Create New Studio button');
    }

    // Look for legal compliance indicators in the studio creation flow
    const legalIndicators = [
      'Business Type', 'Legal Entity', 'Tax ID', 'EIN',
      'Authorized Signer', 'Legal Compliance', 'Company Type',
      'Incorporation', 'Business Address', 'Signatory Information'
    ];

    let foundLegalFeatures = 0;
    for (const indicator of legalIndicators) {
      const element = page.locator(`text=${indicator}`).first();
      if (await element.isVisible()) {
        console.log(`✅ Found legal feature: ${indicator}`);
        foundLegalFeatures++;
      }
    }

    console.log(`📊 Legal compliance features found: ${foundLegalFeatures}/${legalIndicators.length}`);
    expect(foundLegalFeatures).toBeGreaterThan(0);

    console.log('✅ Legal compliance integration verified');
  });
});
