import React, { useState, useEffect } from 'react';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import DatePicker from 'react-datepicker';
import projectTemplates from '../../../data/project-templates';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Input,
  Textarea,
  Select,
  SelectItem,
  Accordion,
  AccordionItem,
  Switch
} from '../../ui/heroui';

const ProjectBasics = ({ projectData, setProjectData }) => {
  const [uploading, setUploading] = useState(false);

  // Project types
  const projectTypes = [
    { value: 'game', label: 'Game' },
    { value: 'app', label: 'App' },
    { value: 'website', label: 'Website' },
    { value: 'plugin', label: 'Plugin/Extension' },
    { value: 'art', label: 'Art/Asset Pack' },
    { value: 'music', label: 'Music/Sound' },
    { value: 'other', label: 'Other' }
  ];

  // State for template selection
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(null);

  // Apply template when project type changes
  useEffect(() => {
    // Only suggest template if this is a new project (name is empty)
    if (projectData.project_type && !projectData.name) {
      setShowTemplateModal(true);
    }
  }, [projectData.project_type]);

  // Apply selected template
  const applyTemplate = (templateKey) => {
    const template = projectTemplates[templateKey];
    if (!template) {
      toast.error('Template not found');
      return;
    }

    // Keep the current project type and any existing data
    const updatedData = {
      ...template,
      project_type: projectData.project_type,
      // Preserve any existing data that shouldn't be overwritten
      thumbnail_url: projectData.thumbnail_url || template.thumbnail_url || '',
      start_date: projectData.start_date || template.start_date || new Date(),
      launch_date: projectData.launch_date || template.launch_date || null
    };

    setProjectData(updatedData);
    setShowTemplateModal(false);
    toast.success(`Applied ${templateKey} template`);
  };

  // Skip template
  const skipTemplate = () => {
    setShowTemplateModal(false);
    toast.info('Template skipped');
  };

  // Handle thumbnail upload
  const handleThumbnailUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Validate file type
    const fileExt = file.name.split('.').pop();
    const allowedExts = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    if (!allowedExts.includes(fileExt.toLowerCase())) {
      toast.error('Invalid file type. Please upload an image file.');
      return;
    }

    // Validate file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      toast.error('File size too large. Maximum size is 2MB.');
      return;
    }

    setUploading(true);
    const uploadToastId = toast.loading('Uploading thumbnail...');

    try {
      // Create a unique file name
      const fileName = `project-${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
      const filePath = `project-thumbnails/${fileName}`;

      // Use the avatars bucket
      const bucketName = 'avatars';

      // Upload to Supabase Storage
      const { data, error: uploadError } = await supabase.storage
        .from(bucketName)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: true
        });

      if (uploadError) {
        // If the error is because the bucket doesn't exist, try to create it
        if (uploadError.message.includes('bucket') && uploadError.message.includes('not found')) {
          toast.error('Storage bucket not found. Please contact an administrator.', { id: uploadToastId });
          setUploading(false);
          return;
        }
        throw new Error(`Upload error: ${uploadError.message}`);
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from(bucketName)
        .getPublicUrl(filePath);

      if (!urlData || !urlData.publicUrl) {
        throw new Error('Failed to get public URL for uploaded file');
      }

      // Update project data
      setProjectData({
        ...projectData,
        thumbnail_url: urlData.publicUrl
      });

      toast.success('Thumbnail uploaded successfully', { id: uploadToastId });
    } catch (error) {
      console.error('Error uploading thumbnail:', error);
      toast.error(`Failed to upload thumbnail: ${error.message}`, { id: uploadToastId });
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="wizard-step-content">
      {/* Step Header */}
      <div className="mb-8">
        <h2 className="wizard-heading-responsive text-foreground mb-2">Project Basics</h2>
        <p className="wizard-text-responsive text-default-600">
          Let's start with the basic information about your project. This will help us set up the foundation for your collaboration.
        </p>
      </div>

      {/* Template Selection Modal */}
      {showTemplateModal && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-lg mx-4 shadow-2xl">
            <CardHeader>
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-semibold">Use Project Template?</h3>
                <Button
                  variant="light"
                  size="sm"
                  onClick={skipTemplate}
                  aria-label="Close"
                  isIconOnly
                >
                  <i className="bi bi-x-lg"></i>
                </Button>
              </div>
            </CardHeader>
            <CardBody className="space-y-6">
              <p className="text-default-600">
                Would you like to use a template for your {projectData.project_type} project?
                This will pre-populate settings, milestones, and contribution tracking.
              </p>

              <div className="p-4 bg-default-100 rounded-lg">
                <h4 className="font-semibold mb-3 text-foreground">Template includes:</h4>
                <ul className="text-sm text-default-600 space-y-2">
                  <li className="flex items-center">
                    <i className="bi bi-check-circle-fill text-success mr-2"></i>
                    Pre-configured royalty model
                  </li>
                  <li className="flex items-center">
                    <i className="bi bi-check-circle-fill text-success mr-2"></i>
                    Relevant contribution categories and task types
                  </li>
                  <li className="flex items-center">
                    <i className="bi bi-check-circle-fill text-success mr-2"></i>
                    Standard milestones for {projectData.project_type} projects
                  </li>
                  <li className="flex items-center">
                    <i className="bi bi-check-circle-fill text-success mr-2"></i>
                    Recommended revenue tranches
                  </li>
                </ul>
              </div>

              <div className="flex gap-3 pt-2">
                <Button
                  onClick={() => applyTemplate(projectData.project_type)}
                  className="flex-1"
                  color="primary"
                  size="lg"
                >
                  <i className="bi bi-magic mr-2"></i>
                  Use Template
                </Button>
                <Button
                  variant="bordered"
                  onClick={skipTemplate}
                  className="flex-1"
                  size="lg"
                >
                  Start from Scratch
                </Button>
              </div>
            </CardBody>
          </Card>
        </div>
      )}

      {/* Main Content Grid */}
      <div className="wizard-grid wizard-grid-3">
        {/* Left Column - Main Form Fields */}
        <div className="xl:col-span-2 wizard-space-y">
          {/* Basic Project Information */}
          <div className="wizard-card">
            <div className="wizard-section">
              <div className="studio-creation-section-header">
                <div className="studio-creation-section-icon">
                  <i className="bi bi-info-circle text-xl"></i>
                </div>
                <h3 className="studio-creation-section-title">Basic Information</h3>
              </div>

              <div className="wizard-space-y">
                <div className="wizard-form-field">
                  <Input
                    label="Project Name"
                    placeholder="Enter a clear, descriptive project name"
                    value={projectData.name || ""}
                    onValueChange={(value) => setProjectData({ ...projectData, name: value })}
                    isRequired
                    description="This will be the main identifier for your project"
                    size="lg"
                    variant="bordered"
                    classNames={{
                      input: "text-lg",
                      inputWrapper: "h-14"
                    }}
                  />
                </div>

                <div className="wizard-form-field">
                  <Textarea
                    label="Project Description"
                    placeholder="Describe what your project is about, its goals, and what makes it unique"
                    value={projectData.description || ""}
                    onValueChange={(value) => setProjectData({ ...projectData, description: value })}
                    minRows={4}
                    description="A clear description helps potential contributors understand your vision"
                    variant="bordered"
                    classNames={{
                      input: "text-base"
                    }}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Project Type & Configuration */}
          <div className="wizard-card">
            <div className="wizard-section">
              <div className="studio-creation-section-header">
                <div className="studio-creation-section-icon">
                  <i className="bi bi-gear text-xl"></i>
                </div>
                <h3 className="studio-creation-section-title">Project Configuration</h3>
              </div>

              <div className="wizard-space-y">
                <div className="wizard-form-field">
                  <Select
                    label="Project Type"
                    placeholder="Select your project type"
                    selectedKeys={projectData.project_type ? [projectData.project_type] : []}
                    onSelectionChange={(keys) => {
                      const selectedKey = Array.from(keys)[0];
                      setProjectData({ ...projectData, project_type: selectedKey });
                    }}
                    description="Choose the type that best describes your project"
                    size="lg"
                    variant="bordered"
                    classNames={{
                      trigger: "h-14"
                    }}
                  >
                    {projectTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </Select>
                </div>

                <div className="wizard-grid wizard-grid-2">
                  <div className="wizard-form-field">
                    <Input
                      label="Estimated Duration (months)"
                      type="number"
                      min="1"
                      max="60"
                      value={projectData.estimated_duration?.toString() || "6"}
                      onValueChange={(value) => setProjectData({ ...projectData, estimated_duration: parseInt(value) || 6 })}
                      description="How long do you expect this project to take?"
                      size="lg"
                      variant="bordered"
                      classNames={{
                        inputWrapper: "h-14"
                      }}
                    />
                  </div>

                  <div className="wizard-form-field">
                    <Input
                      label="Launch Date"
                      type="date"
                      value={projectData.launch_date ? new Date(projectData.launch_date).toISOString().split('T')[0] : ""}
                      onValueChange={(value) => {
                        const date = value ? new Date(value) : null;
                        setProjectData({ ...projectData, launch_date: date });
                      }}
                      description="When do you plan to launch? (Optional)"
                      size="lg"
                      variant="bordered"
                      classNames={{
                        inputWrapper: "h-14"
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Company Information Section */}
          <div className="wizard-card">
            <div className="wizard-section">
              <div className="studio-creation-section-header">
                <div className="studio-creation-section-icon">
                  <i className="bi bi-building text-xl"></i>
                </div>
                <div>
                  <h3 className="studio-creation-section-title">Company Information</h3>
                  <p className="text-sm text-default-500">Required for legal agreement generation</p>
                </div>
              </div>

              <div className="wizard-space-y">
                <div className="wizard-grid wizard-grid-2">
                  <div className="wizard-form-field">
                    <Input
                      label="Company Name"
                      value={projectData.company_name || ''}
                      onValueChange={(value) => setProjectData({ ...projectData, company_name: value })}
                      placeholder="Enter your company name"
                      description="Legal name of the company that owns this project"
                      isRequired
                      size="lg"
                      variant="bordered"
                      classNames={{
                        inputWrapper: "h-14"
                      }}
                    />
                  </div>

                  <div className="wizard-form-field">
                    <Input
                      label="Contact Email"
                      type="email"
                      value={projectData.company_email || ''}
                      onValueChange={(value) => setProjectData({ ...projectData, company_email: value })}
                      placeholder="<EMAIL>"
                      description="Primary contact email for this project"
                      size="lg"
                      variant="bordered"
                      classNames={{
                        inputWrapper: "h-14"
                      }}
                    />
                  </div>
                </div>

                <div className="wizard-form-field">
                  <Input
                    label="Company Address"
                    value={projectData.company_address || ''}
                    onValueChange={(value) => setProjectData({ ...projectData, company_address: value })}
                    placeholder="Enter full company address"
                    description="Complete street address of your company"
                    size="lg"
                    variant="bordered"
                    classNames={{
                      inputWrapper: "h-14"
                    }}
                  />
                </div>

                <div className="wizard-grid wizard-grid-3">
                  <div className="wizard-form-field">
                    <Input
                      label="City"
                      value={projectData.company_city || ''}
                      onValueChange={(value) => setProjectData({ ...projectData, company_city: value })}
                      placeholder="Enter city"
                      description="City where your company is located"
                      size="lg"
                      variant="bordered"
                      classNames={{
                        inputWrapper: "h-14"
                      }}
                    />
                  </div>

                  <div className="wizard-form-field">
                    <Input
                      label="State/Province"
                      value={projectData.company_state || ''}
                      onValueChange={(value) => setProjectData({ ...projectData, company_state: value })}
                      placeholder="Enter state or province"
                      size="lg"
                      variant="bordered"
                      classNames={{
                        inputWrapper: "h-14"
                      }}
                    />
                  </div>

                  <div className="wizard-form-field">
                    <Input
                      label="County/Region"
                      value={projectData.company_county || ''}
                      onValueChange={(value) => setProjectData({ ...projectData, company_county: value })}
                      placeholder="Enter county or region"
                      size="lg"
                      variant="bordered"
                      classNames={{
                        inputWrapper: "h-14"
                      }}
                    />
                  </div>
                </div>

                <div className="wizard-form-field">
                  <Input
                    label="Authorized Signer Name"
                    value={projectData.signer_name || ''}
                    onValueChange={(value) => setProjectData({ ...projectData, signer_name: value })}
                    placeholder="Full name of authorized signer"
                    description="Person who will sign legal agreements on behalf of the company"
                    isRequired
                    size="lg"
                    variant="bordered"
                    classNames={{
                      inputWrapper: "h-14"
                    }}
                  />
                </div>

                <div className="wizard-form-field">
                  <Input
                    label="Signer Title"
                    value={projectData.signer_title || ''}
                    onValueChange={(value) => setProjectData({ ...projectData, signer_title: value })}
                    placeholder="e.g., CEO, Founder, Director"
                    description="Title of the person who will sign agreements"
                    size="lg"
                    variant="bordered"
                    classNames={{
                      inputWrapper: "h-14"
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Thumbnail & Privacy */}
        <div className="wizard-space-y">
          {/* Project Thumbnail */}
          <div className="wizard-card">
            <div className="wizard-section">
              <div className="studio-creation-section-header">
                <div className="studio-creation-section-icon">
                  <i className="bi bi-image text-xl"></i>
                </div>
                <h3 className="studio-creation-section-title">Project Thumbnail</h3>
              </div>

              <div className="wizard-space-y">
                {projectData.thumbnail_url ? (
                  <div className="text-center">
                    <img
                      src={projectData.thumbnail_url}
                      alt="Project thumbnail"
                      className="w-full h-48 object-cover rounded-lg mb-4 border border-default-200"
                    />
                    <Button
                      variant="flat"
                      color="danger"
                      size="sm"
                      onClick={() => setProjectData({ ...projectData, thumbnail_url: '' })}
                    >
                      Remove Thumbnail
                    </Button>
                  </div>
                ) : (
                  <div className="text-center">
                    <div className="w-full h-48 bg-default-100 rounded-lg border-2 border-dashed border-default-300 flex items-center justify-center mb-4">
                      <div className="text-center">
                        <i className="bi bi-image text-4xl text-default-400 mb-2"></i>
                        <p className="text-sm text-default-500">No thumbnail uploaded</p>
                      </div>
                    </div>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleThumbnailUpload}
                      className="hidden"
                      id="thumbnail-upload"
                      disabled={uploading}
                    />
                    <label htmlFor="thumbnail-upload">
                      <Button
                        variant="bordered"
                        size="sm"
                        as="span"
                        disabled={uploading}
                        className="cursor-pointer"
                      >
                        {uploading ? 'Uploading...' : 'Upload Thumbnail'}
                      </Button>
                    </label>
                  </div>
                )}
                <p className="text-xs text-default-500 text-center">
                  Upload a thumbnail image for your project (max 2MB, JPG, PNG, GIF, WebP)
                </p>
              </div>
            </div>
          </div>

          {/* Project Privacy */}
          <div className="wizard-card">
            <div className="wizard-section">
              <div className="studio-creation-section-header">
                <div className="studio-creation-section-icon">
                  <i className="bi bi-shield-check text-xl"></i>
                </div>
                <h3 className="studio-creation-section-title">Project Privacy</h3>
              </div>

              <div className="wizard-space-y">
                <div className="flex items-center justify-between p-4 bg-default-50 rounded-lg border border-default-200">
                  <div className="flex items-center">
                    {projectData.is_public ? (
                      <i className="bi bi-globe text-success text-xl mr-3"></i>
                    ) : (
                      <i className="bi bi-lock text-warning text-xl mr-3"></i>
                    )}
                    <div>
                      <p className="font-semibold">
                        {projectData.is_public ? 'Public Project' : 'Private Project'}
                      </p>
                      <p className="text-sm text-default-500">
                        {projectData.is_public
                          ? 'Anyone can discover and view your project'
                          : 'Only invited members can see your project'
                        }
                      </p>
                    </div>
                  </div>
                  <Switch
                    isSelected={projectData.is_public}
                    onValueChange={(value) => setProjectData({ ...projectData, is_public: value })}
                    color="primary"
                    size="lg"
                  />
                </div>

                {projectData.is_public && (
                  <div className="p-4 bg-primary-50 rounded-lg border border-primary-200">
                    <div className="flex items-start">
                      <i className="bi bi-info-circle text-primary text-lg mr-2 mt-0.5"></i>
                      <div>
                        <p className="font-medium text-primary mb-1">Public Project Benefits</p>
                        <ul className="text-sm text-primary-700 space-y-1">
                          <li>• Attract more contributors</li>
                          <li>• Showcase your work</li>
                          <li>• Build community interest</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectBasics;
