import React, { useState, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardBody, Button, Input, Select, SelectItem, Textarea } from '@heroui/react';
import { UserContext } from '../../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { Building2, Users, Sparkles, ArrowRight, Plus } from 'lucide-react';

/**
 * StudioSelectionStep Component
 * 
 * Cutting-edge UX for studio selection/creation during project creation
 * Seamlessly integrates studio creation into project workflow
 */
const StudioSelectionStep = ({ 
  userStudios, 
  selectedStudioId, 
  onStudioSelect, 
  onStudioCreated,
  onSkip 
}) => {
  const { currentUser } = useContext(UserContext);
  const [showCreation, setShowCreation] = useState(userStudios.length === 0);
  const [isCreating, setIsCreating] = useState(false);
  
  // Studio creation form data (minimal for seamless UX)
  const [studioData, setStudioData] = useState({
    name: '',
    industry: '',
    location: ''
  });

  // Handle studio creation with smart defaults
  const handleCreateStudio = async () => {
    if (!studioData.name.trim()) {
      toast.error('Studio name is required');
      return;
    }

    setIsCreating(true);
    const loadingToastId = toast.loading('Creating your studio...');

    try {
      // Create studio with smart defaults
      const { data: studio, error: studioError } = await supabase
        .from('teams')
        .insert([{
          name: studioData.name.trim(),
          description: `${studioData.name} - Created for project collaboration`,
          studio_type: 'emerging',
          industry: studioData.industry || 'technology',
          is_public: false, // Private by default for new studios
          max_members: 10,
          business_model: 'collaborative',
          created_by: currentUser.id,
          // Map location to business address for agreement generation
          business_address: studioData.location ? {
            address: studioData.location,
            city: studioData.location.split(',')[0]?.trim() || '',
            state: studioData.location.split(',')[1]?.trim() || '',
            country: 'United States'
          } : null
        }])
        .select()
        .single();

      if (studioError) throw studioError;

      // Add creator as founder
      const { error: memberError } = await supabase
        .from('team_members')
        .insert([{
          team_id: studio.id,
          user_id: currentUser.id,
          role: 'founder',
          status: 'active',
          collaboration_type: 'studio_member',
          engagement_duration: 'permanent',
          is_admin: true
        }]);

      if (memberError) throw memberError;

      toast.dismiss(loadingToastId);
      toast.success('Studio created successfully!');
      
      // Notify parent component
      onStudioCreated(studio);
      
    } catch (error) {
      console.error('Error creating studio:', error);
      toast.dismiss(loadingToastId);
      toast.error('Failed to create studio. Please try again.');
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header with contextual explanation */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center mb-8"
      >
        <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
          <Building2 className="w-8 h-8 text-white" />
        </div>
        <h2 className="text-3xl font-bold mb-3">Choose Your Studio</h2>
        <p className="text-lg text-default-600 max-w-2xl mx-auto">
          Studios help us generate legal agreements and manage your projects. 
          {userStudios.length === 0 
            ? " Let's create your first studio with just a few questions."
            : " Select an existing studio or create a new one."
          }
        </p>
      </motion.div>

      <AnimatePresence mode="wait">
        {showCreation ? (
          // Studio Creation Mini-Wizard
          <motion.div
            key="creation"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="bg-gradient-to-br from-blue-50 to-purple-50 border-2 border-blue-200">
              <CardBody className="p-8">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mr-4">
                    <Sparkles className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold">Create Your Studio</h3>
                    <p className="text-sm text-default-600">Just 3 quick questions to get started</p>
                  </div>
                </div>

                <div className="space-y-6">
                  {/* Question 1: Studio Name */}
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      1. What's your studio name? <span className="text-red-500">*</span>
                    </label>
                    <Input
                      placeholder="e.g., Awesome Games Studio, Creative Collective, etc."
                      value={studioData.name}
                      onChange={(e) => setStudioData({ ...studioData, name: e.target.value })}
                      size="lg"
                      variant="bordered"
                      classNames={{
                        input: "text-lg",
                        inputWrapper: "h-14"
                      }}
                    />
                  </div>

                  {/* Question 2: Industry */}
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      2. What type of work do you do?
                    </label>
                    <Select
                      placeholder="Select your industry"
                      selectedKeys={studioData.industry ? [studioData.industry] : []}
                      onSelectionChange={(keys) => {
                        const selectedKey = Array.from(keys)[0];
                        setStudioData({ ...studioData, industry: selectedKey });
                      }}
                      size="lg"
                      variant="bordered"
                      classNames={{
                        trigger: "h-14"
                      }}
                    >
                      <SelectItem key="technology" value="technology">💻 Technology & Software</SelectItem>
                      <SelectItem key="gaming" value="gaming">🎮 Gaming & Interactive</SelectItem>
                      <SelectItem key="creative" value="creative">🎨 Creative & Design</SelectItem>
                      <SelectItem key="media" value="media">📱 Media & Content</SelectItem>
                      <SelectItem key="consulting" value="consulting">💼 Consulting & Services</SelectItem>
                      <SelectItem key="other" value="other">🔧 Other</SelectItem>
                    </Select>
                  </div>

                  {/* Question 3: Location */}
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      3. Where is your business located?
                    </label>
                    <Input
                      placeholder="e.g., San Francisco, CA or New York, NY"
                      value={studioData.location}
                      onChange={(e) => setStudioData({ ...studioData, location: e.target.value })}
                      size="lg"
                      variant="bordered"
                      description="This helps us generate accurate legal agreements"
                      classNames={{
                        input: "text-lg",
                        inputWrapper: "h-14"
                      }}
                    />
                  </div>
                </div>

                <div className="flex gap-3 mt-8">
                  <Button
                    color="primary"
                    size="lg"
                    onClick={handleCreateStudio}
                    isLoading={isCreating}
                    disabled={!studioData.name.trim()}
                    className="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold"
                    endContent={<ArrowRight className="w-4 h-4" />}
                  >
                    Create Studio & Continue
                  </Button>
                  
                  {userStudios.length > 0 && (
                    <Button
                      variant="bordered"
                      size="lg"
                      onClick={() => setShowCreation(false)}
                      className="px-6"
                    >
                      Cancel
                    </Button>
                  )}
                </div>
              </CardBody>
            </Card>
          </motion.div>
        ) : (
          // Studio Selection
          <motion.div
            key="selection"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.3 }}
          >
            <div className="grid gap-4 mb-6">
              {userStudios.map((studio) => (
                <Card
                  key={studio.id}
                  isPressable
                  className={`transition-all duration-200 ${
                    selectedStudioId === studio.id
                      ? 'ring-2 ring-blue-500 bg-blue-50'
                      : 'hover:shadow-md'
                  }`}
                  onClick={() => onStudioSelect(studio.id)}
                >
                  <CardBody className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-12 h-12 rounded-full bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center mr-4">
                          <Users className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold">{studio.name}</h3>
                          <p className="text-sm text-default-600">
                            {studio.studio_type} • {studio.industry}
                          </p>
                        </div>
                      </div>
                      
                      {selectedStudioId === studio.id && (
                        <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center">
                          <div className="w-2 h-2 rounded-full bg-white"></div>
                        </div>
                      )}
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>

            {/* Create New Studio Option */}
            <Card
              isPressable
              className="border-2 border-dashed border-default-300 hover:border-blue-500 hover:bg-blue-50 transition-all duration-200"
              onClick={() => setShowCreation(true)}
            >
              <CardBody className="p-6 text-center">
                <div className="flex items-center justify-center">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center mr-4">
                    <Plus className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">Create New Studio</h3>
                    <p className="text-sm text-default-600">Start a new studio for this project</p>
                  </div>
                </div>
              </CardBody>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default StudioSelectionStep;
