import { test, expect } from '@playwright/test';
import { Eyes, Target, Configuration, BatchInfo } from '@applitools/eyes-playwright';
import { ApplitoolsResultsAnalyzer } from './applitools-results-analyzer.js';

/**
 * Applitools Eyes Visual AI Testing for Royaltea Platform
 * 
 * This uses Applitools Eyes AI to test the website like a human would:
 * - Visual validation using AI and machine learning
 * - Cross-browser visual testing
 * - Automatic detection of visual bugs and regressions
 * - Human-like navigation and interaction testing
 * - Real visual comparison and analysis
 */

const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

const PRODUCTION_URL = 'https://royalty.technology';

test.describe('Applitools Visual AI Testing', () => {
  let eyes;
  let analyzer;
  let testResults = [];

  test.beforeEach(async ({ page }) => {
    // Initialize Applitools Eyes and Results Analyzer
    eyes = new Eyes();
    analyzer = new ApplitoolsResultsAnalyzer();

    // Configure Eyes
    const configuration = new Configuration();
    configuration.setAppName('Royaltea Platform');
    configuration.setTestName('Production Readiness Visual Testing');

    // Set batch info for grouping tests
    const batchInfo = new BatchInfo('Royaltea Production Readiness');
    batchInfo.setId(process.env.APPLITOOLS_BATCH_ID || `batch-${Date.now()}`);
    configuration.setBatch(batchInfo);

    // Set API key (will be set as environment variable)
    if (process.env.APPLITOOLS_API_KEY) {
      configuration.setApiKey(process.env.APPLITOOLS_API_KEY);
    } else {
      console.warn('⚠️ APPLITOOLS_API_KEY not set - visual testing may not work');
    }

    eyes.setConfiguration(configuration);

    // Navigate to the site
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
  });

  test.afterEach(async () => {
    // Close Eyes and get results (only if Eyes was opened)
    if (eyes) {
      try {
        // Check if Eyes is actually open before trying to close
        const results = await eyes.close(false); // false = don't throw if not open

        if (results) {
          // Analyze results with our custom analyzer
          const analysis = analyzer.analyzeResults(results);
          testResults.push(analysis);

          // CRITICAL: Fail test if login didn't work
          if (results._result && (results._result.isAborted || results._result.status === 'Failed')) {
            throw new Error(`❌ VISUAL TEST FAILED: ${analysis.summary}`);
          }
        }

      } catch (error) {
        // Only log Applitools errors, don't fail the test for Eyes issues
        if (error.message && !error.message.includes('Eyes not open')) {
          console.error('❌ Applitools test failed:', error);
          throw error; // Re-throw to fail the test
        } else {
          console.log('ℹ️ Eyes was not opened for this test (likely due to earlier failure)');
        }
      }
    }
  });

  test('Complete Production Readiness Visual Test', async ({ page }) => {
    console.log('🚀 Starting comprehensive Applitools production readiness test...');

    // Open Eyes for this comprehensive test
    await eyes.open(page, 'Royaltea Platform', 'Complete Production Readiness Test');

    // STEP 1: Login Flow with Validation
    console.log('🔐 Step 1: Testing login flow...');
    await eyes.check('01-Login Page Initial State', Target.window().fully());

    // Perform login with strict validation using exact selectors from page snapshot
    const emailField = page.getByRole('textbox', { name: 'Enter email' });
    const passwordField = page.getByRole('textbox', { name: 'Password' });
    const loginButton = page.getByRole('button', { name: 'Log In' });

    // Validate login elements exist
    expect(await emailField.isVisible(), 'Email field must be visible').toBe(true);
    expect(await passwordField.isVisible(), 'Password field must be visible').toBe(true);
    expect(await loginButton.isVisible(), 'Login button must be visible').toBe(true);

    await emailField.fill(TEST_USER.email);
    await passwordField.fill(TEST_USER.password);
    await eyes.check('02-Login Form Filled', Target.window().fully());

    await loginButton.click();
    await page.waitForTimeout(3000); // Wait for login to process

    // CRITICAL: Validate login success by checking if login form is gone
    const currentUrl = page.url();
    const pageContent = await page.textContent('body');

    // Check if we're no longer on the login form (login form should be gone)
    const hasLoginForm = pageContent.includes('Log In to Your Account');
    const hasMainNavigation = pageContent.includes('Start') && pageContent.includes('Track') && pageContent.includes('Earn');
    const hasDashboardContent = pageContent.includes('Dashboard') || pageContent.includes('Welcome') || hasMainNavigation;

    const loginSuccessful = !hasLoginForm && hasDashboardContent;

    expect(loginSuccessful, `Login must be successful. Has login form: ${hasLoginForm}, Has dashboard: ${hasDashboardContent}, URL: ${currentUrl}`).toBe(true);
    await eyes.check('03-Post-Login Dashboard', Target.window().fully());

    // STEP 2: Main Navigation Testing
    console.log('🧭 Step 2: Testing main navigation...');

    // Test Start page
    const startLink = page.locator('a:has-text("Start"), button:has-text("Start")').first();
    if (await startLink.isVisible()) {
      await startLink.click();
      await page.waitForLoadState('networkidle');
      await eyes.check('04-Start Page - Project Creation Hub', Target.window().fully());

      // Test project creation flow - CRITICAL BUSINESS FUNCTIONALITY
      const createButton = page.locator('button:has-text("Create"), button:has-text("New Project"), button:has-text("Start Project")').first();
      if (await createButton.isVisible()) {
        await createButton.click();
        await page.waitForTimeout(3000); // Wait for wizard to load
        await eyes.check('05-Project Creation Wizard', Target.window().fully());

        // Test studio creation integration (should appear if no studio exists)
        const studioNameField = page.locator('input[placeholder*="studio"], input[placeholder*="Studio"], input[name*="studio"]').first();
        if (await studioNameField.isVisible()) {
          console.log('🏢 Testing studio creation workflow...');
          await studioNameField.fill('Test Production Studio');
          await page.waitForTimeout(1000);
          await eyes.check('05a-Studio Creation Form', Target.window().fully());

          // Try to proceed through studio creation
          const nextButton = page.locator('button:has-text("Next"), button:has-text("Continue"), button:has-text("Create")').first();
          if (await nextButton.isVisible()) {
            await nextButton.click();
            await page.waitForTimeout(2000);
            await eyes.check('05b-Studio Creation Next Step', Target.window().fully());
          }
        }

        // Test project details form
        const projectNameField = page.locator('input[placeholder*="project"], input[placeholder*="Project"], input[name*="project"]').first();
        if (await projectNameField.isVisible()) {
          console.log('📋 Testing project creation form...');
          await projectNameField.fill('Test Production Project');
          await page.waitForTimeout(1000);
          await eyes.check('05c-Project Details Form', Target.window().fully());
        }
      }
    }

    // Test Track page
    const trackLink = page.locator('a:has-text("Track"), button:has-text("Track")').first();
    if (await trackLink.isVisible()) {
      await trackLink.click();
      await page.waitForLoadState('networkidle');
      await eyes.check('06-Track Page - Project Management', Target.window().fully());

      // Test kanban functionality - CRITICAL BUSINESS FUNCTIONALITY
      console.log('📋 Testing kanban task management...');
      const addTaskButton = page.locator('button:has-text("Add"), button:has-text("New"), button:has-text("Create Task"), [title*="add" i]').first();
      if (await addTaskButton.isVisible()) {
        await addTaskButton.click();
        await page.waitForTimeout(2000);
        await eyes.check('07-Task Creation Interface', Target.window().fully());

        // Test task creation form - COMPLETE FORM VALIDATION
        const taskTitleField = page.locator('textbox[name*="Task Title"], input[placeholder*="task"], input[placeholder*="title"]').first();
        if (await taskTitleField.isVisible()) {
          await taskTitleField.fill('Test Production Task');
          await page.waitForTimeout(500);

          // Fill description field (might be required)
          const descriptionField = page.locator('textbox[name*="Description"], textarea[placeholder*="description"]').first();
          if (await descriptionField.isVisible()) {
            await descriptionField.fill('Testing task creation for production readiness validation');
            await page.waitForTimeout(500);
          }

          // Fill due date if required
          const dueDateField = page.locator('textbox[name*="Due Date"], input[type="date"]').first();
          if (await dueDateField.isVisible()) {
            await dueDateField.fill('2025-07-15');
            await page.waitForTimeout(500);
          }

          // Fill estimated hours if required
          const hoursField = page.locator('spinbutton[name*="Estimated Hours"], input[type="number"]').first();
          if (await hoursField.isVisible()) {
            await hoursField.fill('4');
            await page.waitForTimeout(500);
          }

          await eyes.check('07a-Task Form Completely Filled', Target.window().fully());

          // Try to save the task - check if button is enabled
          const saveButton = page.locator('button:has-text("Create Task"), button:has-text("Save"), button:has-text("Add")').first();
          if (await saveButton.isVisible()) {
            const isEnabled = await saveButton.isEnabled();
            console.log(`💡 Save button enabled: ${isEnabled}`);

            if (isEnabled) {
              try {
                // Try multiple click strategies to handle modal overlay
                await saveButton.click({ force: true });
                await page.waitForTimeout(3000);
                await eyes.check('07b-Task Created in Kanban', Target.window().fully());
                console.log('✅ Task creation successful');
              } catch (error) {
                console.log('❌ CRITICAL: Task creation failed due to UI issue - modal overlay blocking button');
                console.log(`Error: ${error.message}`);
                await eyes.check('07b-Task Creation UI Issue', Target.window().fully());

                // Try alternative approach - press Enter or Escape to close modal
                try {
                  await page.keyboard.press('Enter');
                  await page.waitForTimeout(2000);
                  await eyes.check('07c-Alternative Task Creation Attempt', Target.window().fully());
                } catch (altError) {
                  console.log('❌ Alternative task creation also failed');
                }
              }
            } else {
              console.log('❌ CRITICAL: Save button is disabled - form validation issue');
              await eyes.check('07b-Save Button Disabled Issue', Target.window().fully());
            }
          }
        }
      }

      // Test kanban board interaction
      const kanbanCard = page.locator('.kanban-card, [data-testid*="task"], .task-card').first();
      if (await kanbanCard.isVisible()) {
        console.log('🎯 Testing kanban card interaction...');
        await kanbanCard.click();
        await page.waitForTimeout(1000);
        if (eyes.getIsOpen()) {
          await eyes.check('07c-Task Details Modal', Target.window().fully());
        }
      }
    }

    // Test Earn page - CRITICAL BUSINESS FUNCTIONALITY
    const earnLink = page.locator('a:has-text("Earn"), button:has-text("Earn")').first();
    if (await earnLink.isVisible()) {
      await earnLink.click();
      await page.waitForTimeout(3000);
      if (eyes.getIsOpen()) {
        await eyes.check('08-Earn Page - Gigwork Platform', Target.window().fully());
      }

      // Test gigwork functionality
      console.log('💰 Testing gigwork system...');
      try {
        const gigworkButton = page.locator('button:has-text("Browse"), button:has-text("Find Work"), button:has-text("Apply")').first();
        if (await gigworkButton.isVisible()) {
          await gigworkButton.click();
          await page.waitForTimeout(2000);
          if (eyes.getIsOpen()) {
            await eyes.check('08a-Gigwork Listings', Target.window().fully());
          }
        } else {
          console.log('❌ CRITICAL: No gigwork functionality found on Earn page');
          if (eyes.getIsOpen()) {
            await eyes.check('08a-Missing Gigwork Features', Target.window().fully());
          }
        }
      } catch (error) {
        console.log(`❌ Gigwork testing failed: ${error.message}`);
      }

      // Test revenue/payment features
      try {
        const revenueSection = page.locator('[data-testid*="revenue"], [class*="revenue"], [class*="payment"]').first();
        if (await revenueSection.isVisible()) {
          console.log('💳 Testing revenue/payment features...');
          await revenueSection.click();
          await page.waitForTimeout(2000);
          if (eyes.getIsOpen()) {
            await eyes.check('08b-Revenue Dashboard', Target.window().fully());
          }
        } else {
          console.log('❌ CRITICAL: No revenue/payment features found');
          if (eyes.getIsOpen()) {
            await eyes.check('08b-Missing Revenue Features', Target.window().fully());
          }
        }
      } catch (error) {
        console.log(`❌ Revenue testing failed: ${error.message}`);
      }

      // Test Teller integration if available
      try {
        const tellerButton = page.locator('button:has-text("Teller"), button:has-text("Payment"), [data-testid*="teller"]').first();
        if (await tellerButton.isVisible()) {
          console.log('🏦 Testing Teller payment integration...');
          await tellerButton.click();
          await page.waitForTimeout(2000);
          if (eyes.getIsOpen()) {
            await eyes.check('08c-Teller Integration', Target.window().fully());
          }
        } else {
          console.log('❌ CRITICAL: No Teller payment integration found');
          if (eyes.getIsOpen()) {
            await eyes.check('08c-Missing Teller Integration', Target.window().fully());
          }
        }
      } catch (error) {
        console.log(`❌ Teller testing failed: ${error.message}`);
      }
    }

    // FINAL VALIDATION: Test critical business workflows completion
    console.log('✅ Final validation: Testing business workflow completion...');

    try {
      // Navigate back to dashboard to validate overall state
      const dashboardLink = page.locator('a:has-text("Dashboard"), button:has-text("Home"), .logo').first();
      if (await dashboardLink.isVisible()) {
        await dashboardLink.click();
        await page.waitForTimeout(2000);
      }

      if (eyes.getIsOpen()) {
        await eyes.check('09-Final Production State - Business Workflows Tested', Target.window().fully());
      }
    } catch (error) {
      console.log(`❌ Final validation failed: ${error.message}`);
    }

    console.log('✅ Complete production readiness test completed');
  });

  test('Responsive Design Cross-Device Testing', async ({ page }) => {
    console.log('📱 Starting comprehensive responsive design testing...');

    // Login first with validation
    await performVisualLogin(page);

    // Open Eyes for responsive testing
    await eyes.open(page, 'Royaltea Platform', 'Responsive Design Cross-Device Test');

    // Test different viewport sizes with comprehensive coverage
    const viewports = [
      { width: 1920, height: 1080, name: 'Desktop-Large' },
      { width: 1366, height: 768, name: 'Desktop-Standard' },
      { width: 768, height: 1024, name: 'Tablet-Portrait' },
      { width: 375, height: 667, name: 'Mobile-iPhone' }
    ];

    for (const viewport of viewports) {
      console.log(`📐 Testing ${viewport.name} (${viewport.width}x${viewport.height})`);

      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      await page.waitForTimeout(1000);

      // Test all main pages at this viewport
      const pages = [
        { name: 'start', selector: 'a:has-text("Start")' },
        { name: 'track', selector: 'a:has-text("Track")' },
        { name: 'earn', selector: 'a:has-text("Earn")' }
      ];

      for (const pageInfo of pages) {
        const navLink = page.locator(pageInfo.selector).first();
        if (await navLink.isVisible()) {
          await navLink.click();
          await page.waitForLoadState('networkidle');

          // Create descriptive checkpoint name
          const checkpointName = `${pageInfo.name.charAt(0).toUpperCase() + pageInfo.name.slice(1)}-Page-${viewport.name}`;
          await eyes.check(checkpointName, Target.window().fully());

          // Test key interactions on each page
          if (pageInfo.name === 'track') {
            const addButton = page.locator('button:has-text("Add"), [title*="add" i]').first();
            if (await addButton.isVisible()) {
              await addButton.click();
              await page.waitForTimeout(500);
              await eyes.check(`Task-Creation-${viewport.name}`, Target.window().fully());

              // Close modal if it opened
              const closeButton = page.locator('button:has-text("Cancel"), button:has-text("Close"), [aria-label*="close" i]').first();
              if (await closeButton.isVisible()) {
                await closeButton.click();
                await page.waitForTimeout(500);
              }
            }
          }
        }
      }
    }

    console.log('✅ Responsive design testing completed');
  });

  test('Visual AI Responsive Design Testing', async ({ page }) => {
    console.log('📱 Starting Applitools visual AI responsive design testing...');

    // Login first
    await performVisualLogin(page);

    // Open Eyes for responsive testing
    await eyes.open(page, 'Royaltea Platform', 'Responsive Design Visual Test');

    // Test different viewport sizes
    const viewports = [
      { width: 1920, height: 1080, name: 'Desktop Large' },
      { width: 1366, height: 768, name: 'Desktop Standard' },
      { width: 768, height: 1024, name: 'Tablet' },
      { width: 375, height: 667, name: 'Mobile' }
    ];

    for (const viewport of viewports) {
      console.log(`📐 Testing ${viewport.name} (${viewport.width}x${viewport.height})`);
      
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      await page.waitForTimeout(1000);
      
      // Test main pages at this viewport
      const pages = ['start', 'track', 'earn'];
      
      for (const pageName of pages) {
        const navLink = page.locator(`a:has-text("${pageName.charAt(0).toUpperCase() + pageName.slice(1)}")`).first();
        if (await navLink.isVisible()) {
          await navLink.click();
          await page.waitForLoadState('networkidle');
          await eyes.check(`${pageName.charAt(0).toUpperCase() + pageName.slice(1)} Page - ${viewport.name}`, Target.window().fully());
        }
      }
    }

    console.log('✅ Visual AI responsive design testing completed');
  });

});

// Helper function for consistent login across tests with strict validation
async function performVisualLogin(page) {
  console.log('🔐 Attempting login with validation...');

  const emailField = page.getByRole('textbox', { name: 'Enter email' });
  const passwordField = page.getByRole('textbox', { name: 'Password' });
  const loginButton = page.getByRole('button', { name: 'Log In' });

  // Verify login elements are visible
  if (!(await emailField.isVisible()) || !(await passwordField.isVisible()) || !(await loginButton.isVisible())) {
    throw new Error('❌ LOGIN ELEMENTS NOT VISIBLE - Cannot proceed with test. This is a critical failure.');
  }

  // Fill login form
  await emailField.fill(TEST_USER.email);
  await passwordField.fill(TEST_USER.password);
  await loginButton.click();
  await page.waitForTimeout(3000); // Wait for login to process

  // CRITICAL: Validate that login was successful with multiple checks
  const currentUrl = page.url();
  const pageContent = await page.textContent('body');

  // Multiple validation checks for login success (URL stays same, check content changes)
  const hasLoginForm = pageContent.includes('Log In to Your Account');
  const hasMainNavigation = pageContent.includes('Start') && pageContent.includes('Track') && pageContent.includes('Earn');
  const hasDashboardContent = pageContent.includes('Dashboard') || pageContent.includes('Welcome') || hasMainNavigation;

  const loginSuccessful = !hasLoginForm && hasDashboardContent;

  if (!loginSuccessful) {
    console.error('❌ LOGIN VALIDATION FAILED:');
    console.error(`   Current URL: ${currentUrl}`);
    console.error(`   Has login form: ${hasLoginForm}`);
    console.error(`   Has main navigation: ${hasMainNavigation}`);
    console.error(`   Has dashboard content: ${hasDashboardContent}`);
    console.error(`   Page content preview: ${pageContent.substring(0, 300)}`);
    throw new Error(`❌ LOGIN FAILED - Test cannot proceed without successful authentication. Has login form: ${hasLoginForm}, Has dashboard: ${hasDashboardContent}, URL: ${currentUrl}`);
  }

  console.log('✅ Login validation successful - user is authenticated and on main dashboard');
}
