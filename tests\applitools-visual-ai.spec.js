import { test } from '@playwright/test';
import { Eyes, Target, Configuration, BatchInfo } from '@applitools/eyes-playwright';

/**
 * Applitools Eyes Visual AI Testing for Royaltea Platform
 * 
 * This uses Applitools Eyes AI to test the website like a human would:
 * - Visual validation using AI and machine learning
 * - Cross-browser visual testing
 * - Automatic detection of visual bugs and regressions
 * - Human-like navigation and interaction testing
 * - Real visual comparison and analysis
 */

const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

const PRODUCTION_URL = 'https://royalty.technology';

test.describe('Applitools Visual AI Testing', () => {
  let eyes;

  test.beforeEach(async ({ page }) => {
    // Initialize Applitools Eyes
    eyes = new Eyes();
    
    // Configure Eyes
    const configuration = new Configuration();
    configuration.setAppName('Royaltea Platform');
    configuration.setTestName('Production Readiness Visual Testing');
    
    // Set batch info for grouping tests
    const batchInfo = new BatchInfo('Royaltea Production Readiness');
    batchInfo.setId(process.env.APPLITOOLS_BATCH_ID || `batch-${Date.now()}`);
    configuration.setBatch(batchInfo);
    
    // Set API key (will be set as environment variable)
    if (process.env.APPLITOOLS_API_KEY) {
      configuration.setApiKey(process.env.APPLITOOLS_API_KEY);
    } else {
      console.warn('⚠️ APPLITOOLS_API_KEY not set - visual testing may not work');
    }
    
    eyes.setConfiguration(configuration);
    
    // Navigate to the site
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
  });

  test.afterEach(async () => {
    // Close Eyes and get results
    if (eyes) {
      try {
        const results = await eyes.close();
        console.log('👁️ Applitools test results:', results);
      } catch (error) {
        console.error('❌ Applitools test failed:', error);
      }
    }
  });

  test('Visual AI Login Flow Testing', async ({ page }) => {
    console.log('👁️ Starting Applitools visual AI login testing...');

    // Open Eyes for this test
    await eyes.open(page, 'Royaltea Platform', 'Login Flow Visual Test');

    // Check the initial login page visually
    await eyes.check('Login Page Initial State', Target.window().fully());

    // Perform login using visual AI guidance
    console.log('🔐 Performing AI-guided login...');
    
    // Fill login form
    const emailField = page.locator('input[type="email"]').first();
    const passwordField = page.locator('input[type="password"]').first();
    
    await emailField.fill(TEST_USER.email);
    await passwordField.fill(TEST_USER.password);
    
    // Visual checkpoint before login
    await eyes.check('Login Form Filled', Target.window().fully());
    
    // Find and click login button using AI visual detection
    const loginButton = page.locator('button:has-text("Sign In"), button:has-text("Login")').first();
    await loginButton.click();
    await page.waitForLoadState('networkidle');
    
    // Visual checkpoint after login
    await eyes.check('Post-Login Dashboard', Target.window().fully());
    
    console.log('✅ Visual AI login flow completed');
  });

  test('Visual AI Main Navigation Testing', async ({ page }) => {
    console.log('🧭 Starting Applitools visual AI navigation testing...');

    // Login first
    await performVisualLogin(page);

    // Open Eyes for navigation testing
    await eyes.open(page, 'Royaltea Platform', 'Main Navigation Visual Test');

    // Test Start page
    console.log('📍 Testing Start page with visual AI...');
    const startLink = page.locator('a:has-text("Start"), button:has-text("Start")').first();
    if (await startLink.isVisible()) {
      await startLink.click();
      await page.waitForLoadState('networkidle');
      await eyes.check('Start Page - Project Creation Hub', Target.window().fully());
    }

    // Test Track page
    console.log('📋 Testing Track page with visual AI...');
    const trackLink = page.locator('a:has-text("Track"), button:has-text("Track")').first();
    if (await trackLink.isVisible()) {
      await trackLink.click();
      await page.waitForLoadState('networkidle');
      await eyes.check('Track Page - Project Management Hub', Target.window().fully());
      
      // Test kanban board interaction
      const addTaskButton = page.locator('button:has-text("Add"), button:has-text("New"), [title*="add" i]').first();
      if (await addTaskButton.isVisible()) {
        await addTaskButton.click();
        await page.waitForTimeout(1000);
        await eyes.check('Track Page - Task Creation Modal', Target.window().fully());
      }
    }

    // Test Earn page
    console.log('💰 Testing Earn page with visual AI...');
    const earnLink = page.locator('a:has-text("Earn"), button:has-text("Earn")').first();
    if (await earnLink.isVisible()) {
      await earnLink.click();
      await page.waitForLoadState('networkidle');
      await eyes.check('Earn Page - Gigwork Platform', Target.window().fully());
    }

    console.log('✅ Visual AI navigation testing completed');
  });

  test('Visual AI Studio and Project Creation', async ({ page }) => {
    console.log('🏢 Starting Applitools visual AI studio/project creation testing...');

    // Login first
    await performVisualLogin(page);

    // Open Eyes for creation flow testing
    await eyes.open(page, 'Royaltea Platform', 'Studio Project Creation Visual Test');

    // Navigate to Start page
    const startLink = page.locator('a:has-text("Start")').first();
    if (await startLink.isVisible()) {
      await startLink.click();
      await page.waitForLoadState('networkidle');
    }

    // Visual checkpoint of Start page
    await eyes.check('Start Page - Before Project Creation', Target.window().fully());

    // Look for project creation button
    const createButton = page.locator('button:has-text("Create"), button:has-text("New Project")').first();
    if (await createButton.isVisible()) {
      await createButton.click();
      await page.waitForLoadState('networkidle');
      
      // Visual checkpoint of project wizard
      await eyes.check('Project Wizard - Initial State', Target.window().fully());
      
      // Fill studio information if present
      const studioNameField = page.locator('input[placeholder*="studio" i], input[placeholder*="name" i]').first();
      if (await studioNameField.isVisible()) {
        await studioNameField.fill(`Visual AI Test Studio ${Date.now()}`);
        await eyes.check('Project Wizard - Studio Info Filled', Target.window().fully());
        
        // Continue through wizard
        const continueButton = page.locator('button:has-text("Continue"), button:has-text("Next")').first();
        if (await continueButton.isVisible()) {
          await continueButton.click();
          await page.waitForLoadState('networkidle');
          await eyes.check('Project Wizard - After Studio Creation', Target.window().fully());
        }
      }
    }

    console.log('✅ Visual AI studio/project creation testing completed');
  });

  test('Visual AI Responsive Design Testing', async ({ page }) => {
    console.log('📱 Starting Applitools visual AI responsive design testing...');

    // Login first
    await performVisualLogin(page);

    // Open Eyes for responsive testing
    await eyes.open(page, 'Royaltea Platform', 'Responsive Design Visual Test');

    // Test different viewport sizes
    const viewports = [
      { width: 1920, height: 1080, name: 'Desktop Large' },
      { width: 1366, height: 768, name: 'Desktop Standard' },
      { width: 768, height: 1024, name: 'Tablet' },
      { width: 375, height: 667, name: 'Mobile' }
    ];

    for (const viewport of viewports) {
      console.log(`📐 Testing ${viewport.name} (${viewport.width}x${viewport.height})`);
      
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      await page.waitForTimeout(1000);
      
      // Test main pages at this viewport
      const pages = ['start', 'track', 'earn'];
      
      for (const pageName of pages) {
        const navLink = page.locator(`a:has-text("${pageName.charAt(0).toUpperCase() + pageName.slice(1)}")`).first();
        if (await navLink.isVisible()) {
          await navLink.click();
          await page.waitForLoadState('networkidle');
          await eyes.check(`${pageName.charAt(0).toUpperCase() + pageName.slice(1)} Page - ${viewport.name}`, Target.window().fully());
        }
      }
    }

    console.log('✅ Visual AI responsive design testing completed');
  });

});

// Helper function for consistent login across tests
async function performVisualLogin(page) {
  console.log('🔐 Attempting login...');

  const emailField = page.locator('input[type="email"]').first();
  const passwordField = page.locator('input[type="password"]').first();
  const loginButton = page.locator('button:has-text("Sign In")').first();

  // Verify login elements are visible
  if (!(await emailField.isVisible()) || !(await passwordField.isVisible()) || !(await loginButton.isVisible())) {
    throw new Error('❌ Login form elements not visible - cannot proceed with test');
  }

  // Fill login form
  await emailField.fill(TEST_USER.email);
  await passwordField.fill(TEST_USER.password);
  await loginButton.click();
  await page.waitForLoadState('networkidle');

  // CRITICAL: Validate that login was successful
  const currentUrl = page.url();
  const pageContent = await page.textContent('body');

  // Check for login success indicators
  const loginSuccessful =
    !currentUrl.includes('login') &&
    !currentUrl.includes('auth') &&
    (currentUrl.includes('dashboard') ||
     pageContent.includes('Dashboard') ||
     pageContent.includes('Start') ||
     pageContent.includes('Track') ||
     pageContent.includes('Earn') ||
     !pageContent.includes('Sign In'));

  if (!loginSuccessful) {
    console.error('❌ Login failed - still on login page');
    console.error(`Current URL: ${currentUrl}`);
    console.error(`Page content preview: ${pageContent.substring(0, 200)}`);
    throw new Error('❌ LOGIN FAILED - Test cannot proceed without successful authentication');
  }

  console.log('✅ Login successful - proceeding with test');
}
