import React, { useState, useEffect, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Card,
  CardBody,
  Button,
  Tabs,
  Tab,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  useDisclosure
} from '@heroui/react';
import GigworkRequestCreator from './GigworkRequestCreator';
import GigworkRequestBrowser from './GigworkRequestBrowser';
import {
  Plus,
  Search,
  Briefcase,
  TrendingUp,
  Users,
  DollarSign,
  Clock,
  Star
} from 'lucide-react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';

/**
 * Gigwork Hub Component
 * 
 * Main interface for the integrated gigwork system, combining request creation,
 * browsing, and management functionality. Replaces the external gigwork platform
 * with a fully integrated internal system.
 */
const GigworkHub = ({ className = "" }) => {
  const { currentUser } = useContext(UserContext);
  const [activeTab, setActiveTab] = useState('browse');
  const { isOpen: isCreateModalOpen, onOpen: onCreateModalOpen, onOpenChange: onCreateModalChange } = useDisclosure();
  const [stats, setStats] = useState({
    totalOpportunities: 0,
    activeApplications: 0,
    completedProjects: 0,
    totalEarnings: 0
  });
  const [loading, setLoading] = useState(true);

  // Load real gigwork statistics from database
  const loadGigworkStats = async () => {
    if (!currentUser) return;

    try {
      setLoading(true);

      // Get total opportunities (collaboration requests)
      const { data: opportunitiesData, error: opportunitiesError } = await supabase
        .from('collaboration_requests')
        .select('id')
        .eq('status', 'open');

      // Get user's active applications
      const { data: applicationsData, error: applicationsError } = await supabase
        .from('collaboration_request_applications')
        .select('id, status')
        .eq('applicant_id', currentUser.id)
        .in('status', ['pending', 'under_review']);

      // Get user's completed projects (accepted applications with completed status)
      const { data: completedData, error: completedError } = await supabase
        .from('collaboration_request_applications')
        .select('id, status')
        .eq('applicant_id', currentUser.id)
        .eq('status', 'completed');

      // Calculate total earnings from payment transactions
      const { data: earningsData, error: earningsError } = await supabase
        .from('payment_transactions')
        .select('amount, status')
        .eq('to_user_id', currentUser.id)
        .eq('status', 'completed');

      // Log any errors but don't fail completely
      if (opportunitiesError) console.error('Error loading opportunities:', opportunitiesError);
      if (applicationsError) console.error('Error loading applications:', applicationsError);
      if (completedError) console.error('Error loading completed projects:', completedError);
      if (earningsError) console.error('Error loading earnings:', earningsError);

      // Calculate stats from real data
      const totalOpportunities = opportunitiesData?.length || 0;
      const activeApplications = applicationsData?.length || 0;
      const completedProjects = completedData?.length || 0;
      const totalEarnings = earningsData?.reduce((sum, payment) => sum + (payment.amount || 0), 0) || 0;

      setStats({
        totalOpportunities,
        activeApplications,
        completedProjects,
        totalEarnings
      });

    } catch (error) {
      console.error('Error loading gigwork stats:', error);
      // Keep default values on error
    } finally {
      setLoading(false);
    }
  };

  // Load stats on component mount and when user changes
  useEffect(() => {
    loadGigworkStats();
  }, [currentUser]);

  // Handle successful request creation
  const handleRequestCreated = (newRequest) => {
    onCreateModalChange(false);
    // Refresh the browser view and stats
    setActiveTab('browse');
    loadGigworkStats();
  };

  return (
    <div className={`gigwork-hub ${className}`}>
      {/* Hero Section */}
      <Card className="mb-8 bg-gradient-to-r from-purple-500/20 to-blue-500/20 border-purple-500/30">
        <CardBody className="p-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div className="flex-1">
              <h1 className="text-3xl font-bold text-white mb-3">
                🚀 Gigwork Hub
              </h1>
              <p className="text-white/80 text-lg mb-4">
                Your integrated platform for finding opportunities, creating projects, and building your professional network.
              </p>
              <div className="flex flex-wrap gap-3">
                <Button
                  color="primary"
                  size="lg"
                  onPress={onCreateModalOpen}
                  startContent={<Plus size={20} />}
                  className="bg-gradient-to-r from-blue-500 to-purple-500"
                >
                  Create Request
                </Button>
                <Button
                  variant="flat"
                  size="lg"
                  onPress={() => setActiveTab('browse')}
                  startContent={<Search size={20} />}
                >
                  Browse Opportunities
                </Button>
              </div>
            </div>
            
            {/* Quick Stats */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
              <div className="text-center">
                <div className="flex items-center justify-center w-12 h-12 bg-blue-500/20 rounded-lg mb-2 mx-auto">
                  <Briefcase className="text-blue-400" size={20} />
                </div>
                <div className="text-2xl font-bold text-white">
                  {loading ? '...' : stats.totalOpportunities}
                </div>
                <div className="text-white/60 text-sm">Opportunities</div>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center w-12 h-12 bg-green-500/20 rounded-lg mb-2 mx-auto">
                  <TrendingUp className="text-green-400" size={20} />
                </div>
                <div className="text-2xl font-bold text-white">
                  {loading ? '...' : stats.activeApplications}
                </div>
                <div className="text-white/60 text-sm">Active Apps</div>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center w-12 h-12 bg-purple-500/20 rounded-lg mb-2 mx-auto">
                  <Star className="text-purple-400" size={20} />
                </div>
                <div className="text-2xl font-bold text-white">
                  {loading ? '...' : stats.completedProjects}
                </div>
                <div className="text-white/60 text-sm">Completed</div>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center w-12 h-12 bg-yellow-500/20 rounded-lg mb-2 mx-auto">
                  <DollarSign className="text-yellow-400" size={20} />
                </div>
                <div className="text-2xl font-bold text-white">
                  {loading ? '...' : `$${stats.totalEarnings.toLocaleString()}`}
                </div>
                <div className="text-white/60 text-sm">Earned</div>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Navigation Tabs */}
      <Tabs
        selectedKey={activeTab}
        onSelectionChange={setActiveTab}
        className="mb-6"
        size="lg"
      >
        <Tab 
          key="browse" 
          title={
            <div className="flex items-center gap-2">
              <Search size={16} />
              <span>Browse Opportunities</span>
            </div>
          } 
        />
        <Tab 
          key="my-requests" 
          title={
            <div className="flex items-center gap-2">
              <Briefcase size={16} />
              <span>My Requests</span>
            </div>
          } 
        />
        <Tab 
          key="applications" 
          title={
            <div className="flex items-center gap-2">
              <Users size={16} />
              <span>Applications</span>
            </div>
          } 
        />
        <Tab 
          key="analytics" 
          title={
            <div className="flex items-center gap-2">
              <TrendingUp size={16} />
              <span>Analytics</span>
            </div>
          } 
        />
      </Tabs>

      {/* Tab Content */}
      <AnimatePresence mode="wait">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
        >
          {activeTab === 'browse' && (
            <GigworkRequestBrowser />
          )}
          
          {activeTab === 'my-requests' && (
            <Card>
              <CardBody className="p-8 text-center">
                <div className="text-4xl mb-4">📋</div>
                <h3 className="text-xl font-semibold text-white mb-2">My Gigwork Requests</h3>
                <p className="text-white/60 mb-6">
                  Manage your posted opportunities and track applications.
                </p>
                <Button
                  color="primary"
                  onPress={onCreateModalOpen}
                  startContent={<Plus size={16} />}
                >
                  Create Your First Request
                </Button>
              </CardBody>
            </Card>
          )}
          
          {activeTab === 'applications' && (
            <Card>
              <CardBody className="p-8 text-center">
                <div className="text-4xl mb-4">📝</div>
                <h3 className="text-xl font-semibold text-white mb-2">My Applications</h3>
                <p className="text-white/60 mb-6">
                  Track your applications and manage ongoing projects.
                </p>
                <Button
                  color="primary"
                  onPress={() => setActiveTab('browse')}
                  startContent={<Search size={16} />}
                >
                  Browse Opportunities
                </Button>
              </CardBody>
            </Card>
          )}
          
          {activeTab === 'analytics' && (
            <Card>
              <CardBody className="p-8 text-center">
                <div className="text-4xl mb-4">📊</div>
                <h3 className="text-xl font-semibold text-white mb-2">Analytics & Insights</h3>
                <p className="text-white/60 mb-6">
                  View your performance metrics and earnings analytics.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
                  <Card className="bg-white/5">
                    <CardBody className="text-center p-6">
                      <div className="text-2xl font-bold text-success mb-1">85%</div>
                      <div className="text-white/60 text-sm">Success Rate</div>
                    </CardBody>
                  </Card>
                  <Card className="bg-white/5">
                    <CardBody className="text-center p-6">
                      <div className="text-2xl font-bold text-warning mb-1">4.8</div>
                      <div className="text-white/60 text-sm">Avg Rating</div>
                    </CardBody>
                  </Card>
                  <Card className="bg-white/5">
                    <CardBody className="text-center p-6">
                      <div className="text-2xl font-bold text-primary mb-1">12</div>
                      <div className="text-white/60 text-sm">Active Projects</div>
                    </CardBody>
                  </Card>
                </div>
              </CardBody>
            </Card>
          )}
        </motion.div>
      </AnimatePresence>

      {/* Create Request Modal */}
      <Modal 
        isOpen={isCreateModalOpen} 
        onOpenChange={onCreateModalChange}
        size="5xl"
        scrollBehavior="inside"
        classNames={{
          base: "bg-background/95 backdrop-blur-md",
          backdrop: "bg-black/50"
        }}
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <h2 className="text-xl font-semibold">Create Gigwork Request</h2>
            <p className="text-sm text-white/60">
              Post a new opportunity and find the perfect collaborator for your project
            </p>
          </ModalHeader>
          <ModalBody className="pb-6">
            <GigworkRequestCreator
              onSuccess={handleRequestCreated}
              onCancel={() => onCreateModalChange(false)}
            />
          </ModalBody>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default GigworkHub;
