import React, { useState, useEffect, useContext } from 'react';
import { <PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>nt, <PERSON>dal<PERSON>eader, ModalBody, Modal<PERSON>ooter, Button, Card, CardBody, Chip, Switch } from '@heroui/react';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';

/**
 * Payment Method Setup Component - Payment Method Configuration Interface
 * 
 * Features:
 * - Comprehensive payment method management and configuration
 * - Support for multiple payment types (bank accounts, cards, digital wallets)
 * - Real-time verification status and security indicators
 * - Primary payment method selection and preferences
 * - Integration with Plaid and other payment processors
 */
const PaymentMethodSetup = ({ isOpen, onClose, currentUser, onUpdate }) => {
  const { currentUser: contextUser } = useContext(UserContext);
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [loading, setLoading] = useState(true);

  // Use currentUser from context if not provided as prop
  const user = currentUser || contextUser;

  // Load payment methods
  useEffect(() => {
    if (isOpen && user) {
      loadPaymentMethods();
    }
  }, [isOpen, user]);

  const loadPaymentMethods = async () => {
    if (!user) return;

    try {
      setLoading(true);

      // Load real Teller accounts from database
      console.log('🔍 Loading payment methods for user:', user.id);
      const { data: tellerAccounts, error: tellerError } = await supabase
        .from('teller_accounts')
        .select('*')
        .eq('user_id', user.id)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (tellerError) {
        console.error('Error loading Teller accounts:', tellerError);
        throw tellerError;
      }

      console.log('💳 Loaded Teller accounts:', tellerAccounts);

      // Transform Teller accounts to payment method format
      const realPaymentMethods = (tellerAccounts || []).map((account, index) => ({
        id: account.id,
        type: 'bank_account',
        name: account.account_name || `${account.institution_name} Account`,
        details: account.account_mask ? `••••${account.account_mask}` : '••••****',
        bankName: account.institution_name || 'Bank',
        accountType: account.account_type || 'checking',
        status: account.is_verified ? 'verified' : 'pending',
        isPrimary: index === 0, // First account is primary by default
        isActive: account.is_active,
        addedAt: new Date(account.created_at),
        lastUsed: account.balance_last_updated ? new Date(account.balance_last_updated) : null,
        capabilities: [
          'receive',
          ...(account.supports_ach ? ['withdraw'] : []),
          ...(account.supports_wire ? ['wire'] : [])
        ],
        verificationMethod: account.verification_method || 'teller',
        tellerAccountId: account.teller_account_id,
        availableBalance: account.available_balance,
        currentBalance: account.current_balance,
        routingNumber: account.routing_number
      }));

      // If no real accounts, show empty state instead of mock data
      if (realPaymentMethods.length === 0) {
        console.log('⚠️ No Teller accounts found for user');
        setPaymentMethods([]);
      } else {
        console.log('✅ Loaded real payment methods:', realPaymentMethods.length);
        setPaymentMethods(realPaymentMethods);
      }

    } catch (error) {
      console.error('Error loading payment methods:', error);
      toast.error('Failed to load payment methods');
      setPaymentMethods([]); // Show empty state on error
    } finally {
      setLoading(false);
    }
  };

  // Format date
  const formatDate = (date) => {
    if (!date) return 'Never';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get status color
  const getStatusColor = (status) => {
    const colors = {
      'verified': 'success',
      'pending': 'warning',
      'failed': 'danger',
      'expired': 'default'
    };
    return colors[status] || 'default';
  };

  // Get payment method icon
  const getPaymentMethodIcon = (type, details) => {
    const icons = {
      'bank_account': '🏦',
      'debit_card': '💳',
      'credit_card': '💳',
      'digital_wallet': '📱'
    };
    return icons[type] || '💼';
  };

  // Handle primary payment method change
  const handleSetPrimary = async (methodId) => {
    try {
      // Mock API call
      setPaymentMethods(prev => 
        prev.map(method => ({
          ...method,
          isPrimary: method.id === methodId
        }))
      );
      
      toast.success('Primary payment method updated');
      onUpdate?.();
      
    } catch (error) {
      console.error('Error setting primary payment method:', error);
      toast.error('Failed to update primary payment method');
    }
  };

  // Handle payment method activation/deactivation
  const handleToggleActive = async (methodId, isActive) => {
    try {
      // Mock API call
      setPaymentMethods(prev => 
        prev.map(method => 
          method.id === methodId 
            ? { ...method, isActive }
            : method
        )
      );
      
      toast.success(`Payment method ${isActive ? 'activated' : 'deactivated'}`);
      onUpdate?.();
      
    } catch (error) {
      console.error('Error toggling payment method:', error);
      toast.error('Failed to update payment method');
    }
  };

  // Handle payment method removal
  const handleRemoveMethod = async (methodId) => {
    try {
      // Mock API call
      setPaymentMethods(prev => 
        prev.filter(method => method.id !== methodId)
      );
      
      toast.success('Payment method removed');
      onUpdate?.();
      
    } catch (error) {
      console.error('Error removing payment method:', error);
      toast.error('Failed to remove payment method');
    }
  };

  // Handle verification retry
  const handleRetryVerification = async (methodId) => {
    try {
      // Mock verification retry
      toast.info('Verification process restarted');
      
      setTimeout(() => {
        setPaymentMethods(prev => 
          prev.map(method => 
            method.id === methodId 
              ? { ...method, status: 'verified' }
              : method
          )
        );
        toast.success('Payment method verified successfully!');
        onUpdate?.();
      }, 3000);
      
    } catch (error) {
      console.error('Error retrying verification:', error);
      toast.error('Failed to retry verification');
    }
  };

  if (loading) {
    return (
      <Modal isOpen={isOpen} onClose={onClose} size="4xl">
        <ModalContent>
          <ModalBody className="py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-default-600">Loading payment methods...</p>
            </div>
          </ModalBody>
        </ModalContent>
      </Modal>
    );
  }

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      size="4xl"
      scrollBehavior="inside"
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6"
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h2 className="text-2xl font-bold">Payment Method Setup</h2>
          <p className="text-default-600 font-normal">
            Manage your payment methods and preferences
          </p>
        </ModalHeader>
        
        <ModalBody>
          {/* Add New Payment Method */}
          <Card className="mb-6 bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20">
            <CardBody className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold mb-2">Add New Payment Method</h3>
                  <p className="text-default-600">
                    Connect a new bank account, card, or digital wallet
                  </p>
                </div>
                <Button
                  color="primary"
                  size="lg"
                  onClick={() => {
                    // Open add payment method flow
                    toast.info('Add payment method feature coming soon');
                  }}
                >
                  + Add Method
                </Button>
              </div>
            </CardBody>
          </Card>

          {/* Payment Methods List */}
          <div className="space-y-4">
            {paymentMethods.map((method, index) => (
              <motion.div
                key={method.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <Card className={`hover:shadow-lg transition-shadow ${
                  method.isPrimary ? 'ring-2 ring-primary' : ''
                }`}>
                  <CardBody className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-4">
                        <div className="text-3xl">
                          {getPaymentMethodIcon(method.type)}
                        </div>
                        <div>
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="text-lg font-semibold">
                              {method.name}
                            </h3>
                            {method.isPrimary && (
                              <Chip color="primary" size="sm" variant="flat">
                                Primary
                              </Chip>
                            )}
                            <Chip 
                              color={getStatusColor(method.status)} 
                              size="sm" 
                              variant="flat"
                            >
                              {method.status}
                            </Chip>
                          </div>
                          <div className="text-default-600">
                            {method.bankName || method.cardBrand || method.provider} {method.details}
                          </div>
                          {method.expiryDate && (
                            <div className="text-sm text-default-500">
                              Expires {method.expiryDate}
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Switch
                          isSelected={method.isActive}
                          onValueChange={(isActive) => handleToggleActive(method.id, isActive)}
                          size="sm"
                        />
                      </div>
                    </div>

                    {/* Method Details */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <div className="text-sm text-default-500">Added</div>
                        <div className="font-medium">{formatDate(method.addedAt)}</div>
                      </div>
                      <div>
                        <div className="text-sm text-default-500">Last Used</div>
                        <div className="font-medium">{formatDate(method.lastUsed)}</div>
                      </div>
                      <div>
                        <div className="text-sm text-default-500">Capabilities</div>
                        <div className="flex gap-1">
                          {method.capabilities.map((capability) => (
                            <Chip key={capability} size="sm" variant="bordered">
                              {capability}
                            </Chip>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2 flex-wrap">
                      {!method.isPrimary && method.status === 'verified' && (
                        <Button
                          size="sm"
                          color="primary"
                          variant="flat"
                          onClick={() => handleSetPrimary(method.id)}
                        >
                          Set as Primary
                        </Button>
                      )}
                      
                      {method.status === 'pending' && (
                        <Button
                          size="sm"
                          color="warning"
                          variant="flat"
                          onClick={() => handleRetryVerification(method.id)}
                        >
                          Retry Verification
                        </Button>
                      )}
                      
                      {method.status === 'failed' && (
                        <Button
                          size="sm"
                          color="warning"
                          variant="flat"
                          onClick={() => handleRetryVerification(method.id)}
                        >
                          Fix Issues
                        </Button>
                      )}
                      
                      <Button
                        size="sm"
                        color="default"
                        variant="flat"
                        onClick={() => {
                          // Edit payment method
                          toast.info('Edit payment method feature coming soon');
                        }}
                      >
                        Edit
                      </Button>
                      
                      {!method.isPrimary && (
                        <Button
                          size="sm"
                          color="danger"
                          variant="flat"
                          onClick={() => handleRemoveMethod(method.id)}
                        >
                          Remove
                        </Button>
                      )}
                    </div>

                    {/* Verification Info */}
                    {method.status === 'pending' && (
                      <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                        <div className="text-sm text-yellow-700 dark:text-yellow-300">
                          <strong>Verification in progress:</strong> We're verifying your {method.type.replace('_', ' ')} 
                          using {method.verificationMethod.replace('_', ' ')}. This usually takes 1-2 business days.
                        </div>
                      </div>
                    )}
                    
                    {method.status === 'failed' && (
                      <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                        <div className="text-sm text-red-700 dark:text-red-300">
                          <strong>Verification failed:</strong> There was an issue verifying this payment method. 
                          Please check your information and try again.
                        </div>
                      </div>
                    )}
                  </CardBody>
                </Card>
              </motion.div>
            ))}
          </div>

          {/* Empty State */}
          {paymentMethods.length === 0 && (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">💳</div>
              <h3 className="text-xl font-semibold mb-2">No Payment Methods</h3>
              <p className="text-default-600 mb-6">
                Add a payment method to start receiving payments and making withdrawals
              </p>
              <Button
                color="primary"
                size="lg"
                onClick={() => {
                  // Open add payment method flow
                  toast.info('Add payment method feature coming soon');
                }}
              >
                Add Your First Payment Method
              </Button>
            </div>
          )}

          {/* Security Notice */}
          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="flex items-start gap-3">
              <span className="text-blue-500 text-xl">🔒</span>
              <div>
                <div className="font-semibold text-blue-700 dark:text-blue-300 mb-1">
                  Your Security is Our Priority
                </div>
                <div className="text-sm text-blue-600 dark:text-blue-400">
                  All payment information is encrypted and securely stored. We never store your full account numbers 
                  or sensitive financial data on our servers. We use industry-standard security measures and comply 
                  with PCI DSS requirements.
                </div>
              </div>
            </div>
          </div>
        </ModalBody>
        
        <ModalFooter>
          <Button color="danger" variant="flat" onPress={onClose}>
            Close
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default PaymentMethodSetup;
